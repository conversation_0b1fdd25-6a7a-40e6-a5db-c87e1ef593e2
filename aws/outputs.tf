# Website URLs
output "website_url" {
  description = "Main website URL"
  value       = "https://manikanta.me"
}

output "website_url_www" {
  description = "WWW website URL"
  value       = "https://www.manikanta.me"
}

# CloudFront Distribution
output "cloudfront_distribution_id" {
  description = "CloudFront Distribution ID"
  value       = aws_cloudfront_distribution.portfolio.id
}

output "cloudfront_domain_name" {
  description = "CloudFront Distribution Domain Name"
  value       = aws_cloudfront_distribution.portfolio.domain_name
}

# S3 Bucket
output "s3_bucket_name" {
  description = "Name of the S3 bucket"
  value       = aws_s3_bucket.portfolio_bucket.bucket
}

output "s3_bucket_arn" {
  description = "ARN of the S3 bucket"
  value       = aws_s3_bucket.portfolio_bucket.arn
}

# Route53
output "route53_zone_id" {
  description = "Route53 hosted zone ID"
  value       = aws_route53_zone.name.zone_id
}

output "route53_name_servers" {
  description = "Route53 name servers"
  value       = aws_route53_zone.name.name_servers
}

# SSL Certificate
output "ssl_certificate_arn" {
  description = "ACM Certificate ARN"
  value       = aws_acm_certificate.portfolio.arn
}
