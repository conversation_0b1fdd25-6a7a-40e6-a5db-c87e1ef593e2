# SSL Certificate for CloudFront (must be in us-east-1)
resource "aws_acm_certificate" "portfolio" {
  provider          = aws.us_east_1
  domain_name       = "manikanta.me"
  subject_alternative_names = ["www.manikanta.me"]
  validation_method = "DNS"

  tags = {
    Name        = "manikanta-portfolio"
    Environment = "production"
  }

  lifecycle {
    create_before_destroy = true
  }
}

# Certificate validation
resource "aws_acm_certificate_validation" "portfolio" {
  provider        = aws.us_east_1
  certificate_arn = aws_acm_certificate.portfolio.arn
  validation_record_fqdns = [
    for record in aws_route53_record.portfolio_validation : record.fqdn
  ]

  timeouts {
    create = "5m"
  }
}

# DNS validation records
resource "aws_route53_record" "portfolio_validation" {
  provider = aws.us_east_1
  for_each = {
    for dvo in aws_acm_certificate.portfolio.domain_validation_options : dvo.domain_name => {
      name   = dvo.resource_record_name
      record = dvo.resource_record_value
      type   = dvo.resource_record_type
    }
  }

  allow_overwrite = true
  name            = each.value.name
  records         = [each.value.record]
  ttl             = 60
  type            = each.value.type
  zone_id         = aws_route53_zone.name.zone_id
}
