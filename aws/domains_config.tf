resource "aws_route53_zone" "name" {
  name = "manikanta.me"
  tags = {
    "Name"      = "manikanta.me"
    "ManagedBy" = "terraform"
  }
}

# A record for root domain
resource "aws_route53_record" "portfolio_root" {
  zone_id = aws_route53_zone.name.zone_id
  name    = "manikanta.me"
  type    = "A"

  alias {
    name                   = aws_cloudfront_distribution.portfolio.domain_name
    zone_id                = aws_cloudfront_distribution.portfolio.hosted_zone_id
    evaluate_target_health = false
  }
}

# A record for www subdomain
resource "aws_route53_record" "portfolio_www" {
  zone_id = aws_route53_zone.name.zone_id
  name    = "www.manikanta.me"
  type    = "A"

  alias {
    name                   = aws_cloudfront_distribution.portfolio.domain_name
    zone_id                = aws_cloudfront_distribution.portfolio.hosted_zone_id
    evaluate_target_health = false
  }
}

