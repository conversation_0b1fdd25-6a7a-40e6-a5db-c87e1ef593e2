<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="Personal blog">

  <!-- iOS meta tags & icons -->
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="Manikanta">
  <link rel="apple-touch-icon" href="favicon.jpeg">

  <!-- Favicon -->
  <link rel="icon" type="image/jpeg" href="favicon.jpeg"/>

  <title>Manikanta</title>
  <link rel="manifest" href="manifest.json">

  <style>
    body {
      margin: 0;
      padding: 0;
      background-color: #0D0D0D;
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
      overflow: hidden;
    }

    .loading-container {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #0D0D0D 0%, #1A1A1A 100%);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      z-index: 9999;
    }

    .logo-container {
      margin-bottom: 40px;
      animation: logoFloat 1s ease-in-out infinite;
    }

    .logo {
      width: 80px;
      height: 80px;
      border-radius: 16px;
      box-shadow: 0 20px 40px rgba(255, 255, 255, 0.1);
    }

    .loading-text {
      color: #FFFFFF;
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 30px;
      opacity: 0;
      animation: textFadeIn 0.5s ease-in-out 0.2s forwards;
    }

    .loading-spinner {
      width: 50px;
      height: 50px;
      border: 3px solid rgba(255, 255, 255, 0.1);
      border-top: 3px solid #FFFFFF;
      border-radius: 50%;
      animation: spin 0.8s linear infinite;
    }

    .particles {
      position: absolute;
      width: 100%;
      height: 100%;
      overflow: hidden;
    }

    .particle {
      position: absolute;
      width: 2px;
      height: 2px;
      background: rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      animation: float 6s ease-in-out infinite;
    }

    @keyframes logoFloat {
      0%, 100% { transform: translateY(0px); }
      50% { transform: translateY(-10px); }
    }

    @keyframes textFadeIn {
      0% { opacity: 0; transform: translateY(20px); }
      100% { opacity: 1; transform: translateY(0px); }
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    @keyframes float {
      0%, 100% {
        transform: translateY(100vh) translateX(0px);
        opacity: 0;
      }
      20% { opacity: 1; }
      80% { opacity: 1; }
      100% {
        transform: translateY(-100px) translateX(100px);
        opacity: 0;
      }
    }

    /* Hide loading screen when Flutter is ready */
    .flutter-ready .loading-container {
      opacity: 0;
      transition: opacity 0.5s ease-out;
      pointer-events: none;
    }
  </style>
</head>
<body>
  <div class="loading-container" id="loading">
    <div class="particles" id="particles"></div>
    <div class="logo-container">
      <img src="favicon.jpeg" alt="Manikanta" class="logo" />
    </div>
    <div class="loading-text">Manikanta</div>
    <div class="loading-spinner"></div>
  </div>

  <script>
    // Create floating particles
    function createParticles() {
      const particlesContainer = document.getElementById('particles');
      const particleCount = 20;

      for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.className = 'particle';
        particle.style.left = Math.random() * 100 + '%';
        particle.style.animationDelay = Math.random() * 6 + 's';
        particle.style.animationDuration = (Math.random() * 3 + 4) + 's';
        particlesContainer.appendChild(particle);
      }
    }

    // Initialize particles
    createParticles();

    // Hide loading screen when Flutter is ready
    window.addEventListener('flutter-first-frame', function() {
      document.body.classList.add('flutter-ready');
      setTimeout(() => {
        const loading = document.getElementById('loading');
        if (loading) {
          loading.style.display = 'none';
        }
      }, 500);
    });
  </script>

  <script src="flutter_bootstrap.js" async></script>
</body>
</html>
