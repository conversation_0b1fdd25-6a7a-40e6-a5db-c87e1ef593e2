#!/bin/bash

# Portfolio Deployment Script
# This script builds the Flutter web app and deploys it to S3

set -e

echo "🚀 Starting portfolio deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
BUCKET_NAME="manikanta-gopi"
CLOUDFRONT_DISTRIBUTION_ID=""
BUILD_DIR="build/web"

# Function to print colored output
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

# Check if required tools are installed
check_dependencies() {
    print_status "Checking dependencies..."
    
    if ! command -v flutter &> /dev/null; then
        print_error "Flutter is not installed or not in PATH"
        exit 1
    fi
    
    if ! command -v aws &> /dev/null; then
        print_error "AWS CLI is not installed or not in PATH"
        exit 1
    fi
    
    print_status "All dependencies are available"
}

# Build Flutter web app
build_flutter_app() {
    print_status "Building Flutter web app..."
    
    # Clean previous build
    flutter clean
    
    # Get dependencies
    flutter pub get
    
    # Build for web with release mode
    flutter build web --release --web-renderer html
    
    if [ ! -d "$BUILD_DIR" ]; then
        print_error "Build failed - $BUILD_DIR directory not found"
        exit 1
    fi
    
    print_status "Flutter web app built successfully"
}

# Deploy to S3
deploy_to_s3() {
    print_status "Deploying to S3 bucket: $BUCKET_NAME..."
    
    # Sync files to S3
    aws s3 sync "$BUILD_DIR" "s3://$BUCKET_NAME" \
        --delete \
        --cache-control "public, max-age=31536000" \
        --exclude "*.html" \
        --exclude "*.json"
    
    # Upload HTML files with shorter cache
    aws s3 sync "$BUILD_DIR" "s3://$BUCKET_NAME" \
        --delete \
        --cache-control "public, max-age=0, must-revalidate" \
        --include "*.html" \
        --include "*.json"
    
    print_status "Files uploaded to S3 successfully"
}

# Invalidate CloudFront cache
invalidate_cloudfront() {
    if [ -z "$CLOUDFRONT_DISTRIBUTION_ID" ]; then
        print_warning "CloudFront distribution ID not set, skipping cache invalidation"
        print_warning "You can manually invalidate the cache or set CLOUDFRONT_DISTRIBUTION_ID"
        return
    fi
    
    print_status "Invalidating CloudFront cache..."
    
    aws cloudfront create-invalidation \
        --distribution-id "$CLOUDFRONT_DISTRIBUTION_ID" \
        --paths "/*"
    
    print_status "CloudFront cache invalidation initiated"
}

# Get CloudFront distribution ID from Terraform output
get_cloudfront_id() {
    if [ -f "aws/terraform.tfstate" ]; then
        CLOUDFRONT_DISTRIBUTION_ID=$(cd aws && terraform output -raw cloudfront_distribution_id 2>/dev/null || echo "")
    fi
}

# Main deployment process
main() {
    echo "🌟 Portfolio Deployment for manikanta.me"
    echo "========================================"
    
    check_dependencies
    build_flutter_app
    deploy_to_s3
    get_cloudfront_id
    invalidate_cloudfront
    
    echo ""
    print_status "Deployment completed successfully! 🎉"
    echo ""
    echo "Your website should be available at:"
    echo "  • https://manikanta.me"
    echo "  • https://www.manikanta.me"
    echo ""
    print_warning "Note: It may take a few minutes for changes to propagate through CloudFront"
}

# Run main function
main "$@"
